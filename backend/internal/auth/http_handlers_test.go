package auth

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/denilany/mesaka/internal/users"
)

func TestHandler_Signup_Success(t *testing.T) {
	mockService := &MockAuthService{}
	handler := NewHandler(mockService)
	router := setupTestRouter()
	router.POST("/signup", handler.Signup)

	req := users.CreateUserRequest{
		Name:     "<PERSON>",
		Email:    "<EMAIL>",
		Password: "password123",
		Role:     "client",
	}

	expectedResponse := &SignupResponse{
		User: users.UserResponse{
			ID:        1,
			Name:      "<PERSON>",
			Email:     "<EMAIL>",
			Role:      "client",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		AccessToken:    "access-token",
		RefreshTokenID: "refresh-token-id",
	}

	mockService.On("Signup", req).Return(expectedResponse, nil)

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/signup", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusCreated, w.Code)

	var response SignupResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, expectedResponse.User.Name, response.User.Name)
	assert.Equal(t, expectedResponse.User.Email, response.User.Email)
	assert.Equal(t, expectedResponse.AccessToken, response.AccessToken)
	assert.Equal(t, expectedResponse.RefreshTokenID, response.RefreshTokenID)

	mockService.AssertExpectations(t)
}

func TestHandler_Signup_InvalidRequest(t *testing.T) {
	mockService := &MockAuthService{}
	handler := NewHandler(mockService)
	router := setupTestRouter()
	router.POST("/signup", handler.Signup)

	// Invalid request - missing required fields
	invalidReq := map[string]interface{}{
		"name": "John Doe",
		// missing email, password, role
	}

	reqBody, _ := json.Marshal(invalidReq)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/signup", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "invalid_request", response.Error)
}

func TestHandler_Signup_EmailExists(t *testing.T) {
	mockService := &MockAuthService{}
	handler := NewHandler(mockService)
	router := setupTestRouter()
	router.POST("/signup", handler.Signup)

	req := users.CreateUserRequest{
		Name:     "John Doe",
		Email:    "<EMAIL>",
		Password: "password123",
		Role:     "client",
	}

	mockService.On("Signup", req).Return(nil, errors.New("email already exists"))

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/signup", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusConflict, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "email_exists", response.Error)

	mockService.AssertExpectations(t)
}

func TestHandler_Login_Success(t *testing.T) {
	mockService := &MockAuthService{}
	handler := NewHandler(mockService)
	router := setupTestRouter()
	router.POST("/login", handler.Login)

	req := users.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	expectedResponse := &LoginResponse{
		User: users.UserResponse{
			ID:    1,
			Name:  "John Doe",
			Email: "<EMAIL>",
			Role:  "client",
		},
		AccessToken:    "access-token",
		RefreshTokenID: "refresh-token-id",
	}

	mockService.On("Login", req).Return(expectedResponse, nil)

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/login", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusOK, w.Code)

	var response LoginResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, expectedResponse.User.Name, response.User.Name)
	assert.Equal(t, expectedResponse.User.Email, response.User.Email)
	assert.Equal(t, expectedResponse.AccessToken, response.AccessToken)
	assert.Equal(t, expectedResponse.RefreshTokenID, response.RefreshTokenID)

	mockService.AssertExpectations(t)
}

func TestHandler_Login_InvalidCredentials(t *testing.T) {
	mockService := &MockAuthService{}
	handler := NewHandler(mockService)
	router := setupTestRouter()
	router.POST("/login", handler.Login)

	req := users.LoginRequest{
		Email:    "<EMAIL>",
		Password: "wrongpassword",
	}

	mockService.On("Login", req).Return(nil, errors.New("invalid credentials"))

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/login", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "invalid_credentials", response.Error)

	mockService.AssertExpectations(t)
}

func TestHandler_Refresh_Success(t *testing.T) {
	mockService := &MockAuthService{}
	handler := NewHandler(mockService)
	router := setupTestRouter()
	router.POST("/refresh", handler.Refresh)

	req := RefreshRequest{
		RefreshTokenID: "refresh-token-id",
	}

	expectedResponse := &RefreshResponse{
		AccessToken:    "new-access-token",
		RefreshTokenID: "new-refresh-token-id",
	}

	mockService.On("Refresh", req.RefreshTokenID).Return(expectedResponse, nil)

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/refresh", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusOK, w.Code)

	var response RefreshResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, expectedResponse.AccessToken, response.AccessToken)
	assert.Equal(t, expectedResponse.RefreshTokenID, response.RefreshTokenID)

	mockService.AssertExpectations(t)
}

func TestHandler_Logout_Success(t *testing.T) {
	mockService := &MockAuthService{}
	handler := NewHandler(mockService)
	router := setupTestRouter()
	router.POST("/logout", handler.Logout)

	req := RefreshRequest{
		RefreshTokenID: "refresh-token-id",
	}

	mockService.On("Logout", req.RefreshTokenID).Return(nil)

	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/logout", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]string
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, "Logged out successfully", response["message"])

	mockService.AssertExpectations(t)
}
