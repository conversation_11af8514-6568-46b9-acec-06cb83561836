package auth

import (
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewJWTService(t *testing.T) {
	secretKey := "test-secret-key"
	accessTTL := 15 * time.Minute
	refreshTTL := 7 * 24 * time.Hour

	service := NewJWTService(secretKey, accessTTL, refreshTTL)

	assert.NotNil(t, service)
}

func TestJWTService_GenerateAccessToken(t *testing.T) {
	service := NewJWTService("test-secret", 15*time.Minute, 7*24*time.Hour)

	tests := []struct {
		name   string
		userID int
		email  string
		role   string
	}{
		{
			name:   "valid client token",
			userID: 1,
			email:  "<EMAIL>",
			role:   "client",
		},
		{
			name:   "valid chef token",
			userID: 2,
			email:  "<EMAIL>",
			role:   "chef",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := service.GenerateAccessToken(tt.userID, tt.email, tt.role)
			require.NoError(t, err)
			assert.NotEmpty(t, token)

			// Verify token can be parsed
			parsedToken, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
				return []byte("test-secret"), nil
			})

			require.NoError(t, err)
			assert.True(t, parsedToken.Valid)

			claims, ok := parsedToken.Claims.(*Claims)
			require.True(t, ok)
			assert.Equal(t, tt.userID, claims.UserID)
			assert.Equal(t, tt.email, claims.Email)
			assert.Equal(t, tt.role, claims.Role)
		})
	}
}

func TestJWTService_ValidateAccessToken(t *testing.T) {
	service := NewJWTService("test-secret", 15*time.Minute, 7*24*time.Hour)

	t.Run("valid token", func(t *testing.T) {
		userID := 1
		email := "<EMAIL>"
		role := "client"

		token, err := service.GenerateAccessToken(userID, email, role)
		require.NoError(t, err)

		claims, err := service.ValidateAccessToken(token)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, role, claims.Role)
	})

	t.Run("invalid token", func(t *testing.T) {
		claims, err := service.ValidateAccessToken("invalid-token")
		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("token with wrong secret", func(t *testing.T) {
		wrongService := NewJWTService("wrong-secret", 15*time.Minute, 7*24*time.Hour)
		token, err := wrongService.GenerateAccessToken(1, "<EMAIL>", "client")
		require.NoError(t, err)

		claims, err := service.ValidateAccessToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("expired token", func(t *testing.T) {
		expiredService := NewJWTService("test-secret", -1*time.Hour, 7*24*time.Hour)
		token, err := expiredService.GenerateAccessToken(1, "<EMAIL>", "client")
		require.NoError(t, err)

		// Wait a moment to ensure token is expired
		time.Sleep(10 * time.Millisecond)

		claims, err := service.ValidateAccessToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
	})
}

func TestJWTService_ExtractUserIDFromToken(t *testing.T) {
	service := NewJWTService("test-secret", 15*time.Minute, 7*24*time.Hour)

	t.Run("valid token", func(t *testing.T) {
		expectedUserID := 123
		token, err := service.GenerateAccessToken(expectedUserID, "<EMAIL>", "client")
		require.NoError(t, err)

		userID, err := service.ExtractUserIDFromToken(token)
		require.NoError(t, err)
		assert.Equal(t, expectedUserID, userID)
	})

	t.Run("invalid token", func(t *testing.T) {
		userID, err := service.ExtractUserIDFromToken("invalid-token")
		assert.Error(t, err)
		assert.Equal(t, 0, userID)
	})
}
