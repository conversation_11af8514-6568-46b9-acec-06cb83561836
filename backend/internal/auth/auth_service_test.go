package auth

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"

	"github.com/denilany/mesaka/internal/users"
)

func TestNewService(t *testing.T) {
	userRepo := &MockUserRepository{}
	jwtService := &MockJWTService{}
	redisService := &MockRedisService{}

	service := NewService(userRepo, jwtService, redisService)

	assert.NotNil(t, service)
}

func TestService_Signup_Success(t *testing.T) {
	userRepo := &MockUserRepository{}
	jwtService := &MockJWTService{}
	redisService := &MockRedisService{}
	service := NewService(userRepo, jwtService, redisService)

	req := users.CreateUserRequest{
		Name:     "<PERSON>",
		Email:    "<EMAIL>",
		Password: "password123",
		Role:     "client",
	}

	// Mock expectations
	userRepo.On("EmailExists", req.Email).Return(false, nil)
	userRepo.On("Create", mock.MatchedBy(func(user *users.User) bool {
		return user.Name == req.Name && user.Email == req.Email && user.Role == req.Role
	})).Return(nil).Run(func(args mock.Arguments) {
		user := args.Get(0).(*users.User)
		user.ID = 1 // Simulate database setting ID
		user.CreatedAt = time.Now()
		user.UpdatedAt = time.Now()
	})
	jwtService.On("GenerateAccessToken", 1, req.Email, req.Role).Return("access-token", nil)
	redisService.On("StoreRefreshToken", 1, req.Email, req.Role).Return("refresh-token-id", nil)

	response, err := service.Signup(req)

	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, "John Doe", response.User.Name)
	assert.Equal(t, "<EMAIL>", response.User.Email)
	assert.Equal(t, "client", response.User.Role)
	assert.Equal(t, "access-token", response.AccessToken)
	assert.Equal(t, "refresh-token-id", response.RefreshTokenID)

	userRepo.AssertExpectations(t)
	jwtService.AssertExpectations(t)
	redisService.AssertExpectations(t)
}

func TestService_Signup_EmailExists(t *testing.T) {
	userRepo := &MockUserRepository{}
	jwtService := &MockJWTService{}
	redisService := &MockRedisService{}
	service := NewService(userRepo, jwtService, redisService)

	req := users.CreateUserRequest{
		Name:     "John Doe",
		Email:    "<EMAIL>",
		Password: "password123",
		Role:     "client",
	}

	userRepo.On("EmailExists", req.Email).Return(true, nil)

	response, err := service.Signup(req)

	assert.Error(t, err)
	assert.Nil(t, response)
	assert.Contains(t, err.Error(), "email already exists")

	userRepo.AssertExpectations(t)
}

func TestService_Login_Success(t *testing.T) {
	userRepo := &MockUserRepository{}
	jwtService := &MockJWTService{}
	redisService := &MockRedisService{}
	service := NewService(userRepo, jwtService, redisService)

	req := users.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	user := &users.User{
		ID:       1,
		Name:     "John Doe",
		Email:    req.Email,
		Password: string(hashedPassword),
		Role:     "client",
	}

	userRepo.On("GetByEmail", req.Email).Return(user, nil)
	jwtService.On("GenerateAccessToken", user.ID, user.Email, user.Role).Return("access-token", nil)
	redisService.On("StoreRefreshToken", user.ID, user.Email, user.Role).Return("refresh-token-id", nil)

	response, err := service.Login(req)

	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, "John Doe", response.User.Name)
	assert.Equal(t, "<EMAIL>", response.User.Email)
	assert.Equal(t, "client", response.User.Role)
	assert.Equal(t, "access-token", response.AccessToken)
	assert.Equal(t, "refresh-token-id", response.RefreshTokenID)

	userRepo.AssertExpectations(t)
	jwtService.AssertExpectations(t)
	redisService.AssertExpectations(t)
}

func TestService_Login_InvalidCredentials(t *testing.T) {
	userRepo := &MockUserRepository{}
	jwtService := &MockJWTService{}
	redisService := &MockRedisService{}
	service := NewService(userRepo, jwtService, redisService)

	req := users.LoginRequest{
		Email:    "<EMAIL>",
		Password: "wrongpassword",
	}

	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
	user := &users.User{
		ID:       1,
		Name:     "John Doe",
		Email:    req.Email,
		Password: string(hashedPassword),
		Role:     "client",
	}

	userRepo.On("GetByEmail", req.Email).Return(user, nil)

	response, err := service.Login(req)

	assert.Error(t, err)
	assert.Nil(t, response)
	assert.Contains(t, err.Error(), "invalid credentials")

	userRepo.AssertExpectations(t)
}

func TestService_Logout_Success(t *testing.T) {
	userRepo := &MockUserRepository{}
	jwtService := &MockJWTService{}
	redisService := &MockRedisService{}
	service := NewService(userRepo, jwtService, redisService)

	refreshTokenID := "refresh-token-id"

	redisService.On("DeleteRefreshToken", refreshTokenID).Return(nil)

	err := service.Logout(refreshTokenID)

	require.NoError(t, err)
	redisService.AssertExpectations(t)
}

func TestService_GetUserFromToken_Success(t *testing.T) {
	userRepo := &MockUserRepository{}
	jwtService := &MockJWTService{}
	redisService := &MockRedisService{}
	service := NewService(userRepo, jwtService, redisService)

	token := "valid-token"
	userID := 1
	user := &users.User{
		ID:    userID,
		Name:  "John Doe",
		Email: "<EMAIL>",
		Role:  "client",
	}

	jwtService.On("ExtractUserIDFromToken", token).Return(userID, nil)
	userRepo.On("GetByID", userID).Return(user, nil)

	response, err := service.GetUserFromToken(token)

	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, user.ID, response.ID)
	assert.Equal(t, user.Name, response.Name)
	assert.Equal(t, user.Email, response.Email)
	assert.Equal(t, user.Role, response.Role)

	jwtService.AssertExpectations(t)
	userRepo.AssertExpectations(t)
}
