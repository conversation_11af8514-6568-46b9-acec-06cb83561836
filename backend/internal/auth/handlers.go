package auth

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/denilany/mesaka/internal/users"
)

// <PERSON><PERSON> handles HTTP requests for authentication
type Handler struct {
	authService ServiceInterface
}

// NewHandler creates a new authentication handler
func <PERSON>andler(authService ServiceInterface) *Handler {
	return &Handler{
		authService: authService,
	}
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message,omitempty"`
}

// RefreshRequest represents the request payload for token refresh
type RefreshRequest struct {
	RefreshTokenID string `json:"refresh_token_id" binding:"required"`
}

// Signup handles user registration
func (h *Handler) Signup(c *gin.Context) {
	var req users.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: err.Error(),
		})
		return
	}

	response, err := h.authService.Signup(req)
	if err != nil {
		if strings.Contains(err.Error(), "email already exists") {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "email_exists",
				Message: "Email already exists",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "signup_failed",
			Message: "Failed to create user account",
		})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// Login handles user authentication
func (h *Handler) Login(c *gin.Context) {
	var req users.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: err.Error(),
		})
		return
	}

	response, err := h.authService.Login(req)
	if err != nil {
		if strings.Contains(err.Error(), "invalid credentials") {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "invalid_credentials",
				Message: "Invalid email or password",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "login_failed",
			Message: "Failed to authenticate user",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// Refresh handles token refresh
func (h *Handler) Refresh(c *gin.Context) {
	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: err.Error(),
		})
		return
	}

	response, err := h.authService.Refresh(req.RefreshTokenID)
	if err != nil {
		if strings.Contains(err.Error(), "invalid or expired") {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "invalid_refresh_token",
				Message: "Invalid or expired refresh token",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "refresh_failed",
			Message: "Failed to refresh token",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// Logout handles user logout
func (h *Handler) Logout(c *gin.Context) {
	var req RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: err.Error(),
		})
		return
	}

	err := h.authService.Logout(req.RefreshTokenID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "logout_failed",
			Message: "Failed to logout",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
}

// Me returns the current user's information
func (h *Handler) Me(c *gin.Context) {
	// Extract token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "missing_token",
			Message: "Authorization header is required",
		})
		return
	}

	// Check if it's a Bearer token
	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "invalid_token_format",
			Message: "Authorization header must be in format: Bearer <token>",
		})
		return
	}

	token := tokenParts[1]
	user, err := h.authService.GetUserFromToken(token)
	if err != nil {
		if strings.Contains(err.Error(), "invalid token") {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "invalid_token",
				Message: "Invalid or expired token",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "user_fetch_failed",
			Message: "Failed to fetch user information",
		})
		return
	}

	c.JSON(http.StatusOK, user)
}
