package auth

import (
	"github.com/gin-gonic/gin"
)

// SetupRoutes configures the authentication routes
func SetupRoutes(router *gin.RouterGroup, handler *Handler) {
	// Public routes (no authentication required)
	router.POST("/signup", handler.Signup)
	router.POST("/login", handler.Login)
	router.POST("/refresh", handler.Refresh)
	router.POST("/logout", handler.Logout)

	// Protected routes (authentication required)
	router.GET("/me", handler.Me)
}
