package auth

import (
	"fmt"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/go-redis/redismock/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewRedisService(t *testing.T) {
	client, _ := redismock.NewClientMock()
	ttl := 7 * 24 * time.Hour

	service := NewRedisService(client, ttl)

	assert.NotNil(t, service)
}

func TestRedisService_StoreRefreshToken(t *testing.T) {
	client, mock := redismock.NewClientMock()
	service := NewRedisService(client, 7*24*time.Hour)

	userID := 1
	email := "<EMAIL>"
	role := "client"

	t.Run("successful store", func(t *testing.T) {
		mock.Regexp().ExpectSet(`refresh_token:.*`, `.*`, 7*24*time.Hour).SetVal("OK")

		tokenID, err := service.StoreRefreshToken(userID, email, role)
		require.NoError(t, err)
		assert.NotEmpty(t, tokenID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis error", func(t *testing.T) {
		mock.Regexp().ExpectSet(`refresh_token:.*`, `.*`, 7*24*time.Hour).SetErr(redis.TxFailedErr)

		tokenID, err := service.StoreRefreshToken(userID, email, role)
		assert.Error(t, err)
		assert.Empty(t, tokenID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisService_GetRefreshToken(t *testing.T) {
	client, mock := redismock.NewClientMock()
	service := NewRedisService(client, 7*24*time.Hour)

	tokenID := "test-token-id"
	expectedData := RefreshTokenData{
		UserID:    1,
		Email:     "<EMAIL>",
		Role:      "client",
		CreatedAt: time.Now(),
	}

	t.Run("successful get", func(t *testing.T) {
		// Mock the JSON data that would be stored with future expiration
		futureTime := time.Now().Add(24 * time.Hour).Format(time.RFC3339)
		jsonData := fmt.Sprintf(`{"user_id":1,"email":"<EMAIL>","role":"client","created_at":"2023-01-01T00:00:00Z","expires_at":"%s"}`, futureTime)
		mock.ExpectGet("refresh_token:" + tokenID).SetVal(jsonData)

		data, err := service.GetRefreshToken(tokenID)
		require.NoError(t, err)
		assert.Equal(t, expectedData.UserID, data.UserID)
		assert.Equal(t, expectedData.Email, data.Email)
		assert.Equal(t, expectedData.Role, data.Role)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("token not found", func(t *testing.T) {
		mock.ExpectGet("refresh_token:" + tokenID).RedisNil()

		data, err := service.GetRefreshToken(tokenID)
		assert.Error(t, err)
		assert.Nil(t, data)
		assert.Contains(t, err.Error(), "refresh token not found")

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis error", func(t *testing.T) {
		mock.ExpectGet("refresh_token:" + tokenID).SetErr(redis.TxFailedErr)

		data, err := service.GetRefreshToken(tokenID)
		assert.Error(t, err)
		assert.Nil(t, data)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisService_DeleteRefreshToken(t *testing.T) {
	client, mock := redismock.NewClientMock()
	service := NewRedisService(client, 7*24*time.Hour)

	refreshTokenID := "test-refresh-token-id"

	t.Run("successful delete", func(t *testing.T) {
		mock.ExpectDel("refresh_token:" + refreshTokenID).SetVal(1)

		err := service.DeleteRefreshToken(refreshTokenID)
		require.NoError(t, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("token not found", func(t *testing.T) {
		mock.ExpectDel("refresh_token:" + refreshTokenID).SetVal(0)

		err := service.DeleteRefreshToken(refreshTokenID)
		assert.NoError(t, err) // Redis delete doesn't error when key doesn't exist

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("redis error", func(t *testing.T) {
		mock.ExpectDel("refresh_token:" + refreshTokenID).SetErr(redis.TxFailedErr)

		err := service.DeleteRefreshToken(refreshTokenID)
		assert.Error(t, err)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisService_RotateRefreshToken(t *testing.T) {
	client, mock := redismock.NewClientMock()
	service := NewRedisService(client, 7*24*time.Hour)

	oldTokenID := "old-token-id"
	userID := 1
	email := "<EMAIL>"
	role := "client"

	t.Run("successful rotation", func(t *testing.T) {
		// Expect storage of new token
		mock.Regexp().ExpectSet(`refresh_token:.*`, `.*`, 7*24*time.Hour).SetVal("OK")
		// Expect deletion of old token
		mock.ExpectDel("refresh_token:" + oldTokenID).SetVal(1)

		newTokenID, err := service.RotateRefreshToken(oldTokenID, userID, email, role)
		require.NoError(t, err)
		assert.NotEmpty(t, newTokenID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("store error", func(t *testing.T) {
		mock.Regexp().ExpectSet(`refresh_token:.*`, `.*`, 7*24*time.Hour).SetErr(redis.TxFailedErr)

		newTokenID, err := service.RotateRefreshToken(oldTokenID, userID, email, role)
		assert.Error(t, err)
		assert.Empty(t, newTokenID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
