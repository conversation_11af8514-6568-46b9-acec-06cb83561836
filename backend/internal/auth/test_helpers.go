package auth

import (
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/mock"

	"github.com/denilany/mesaka/internal/users"
)

// Test helper functions and mocks for auth package tests

// setupTestRouter creates a test Gin router
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	return gin.New()
}

// MockAuthService is a mock implementation of the auth Service
type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) Signup(req users.CreateUserRequest) (*SignupResponse, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*SignupResponse), args.Error(1)
}

func (m *MockAuthService) Login(req users.LoginRequest) (*LoginResponse, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*LoginResponse), args.Error(1)
}

func (m *MockAuthService) Refresh(refreshTokenID string) (*RefreshResponse, error) {
	args := m.Called(refreshTokenID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*RefreshResponse), args.Error(1)
}

func (m *MockAuthService) Logout(refreshTokenID string) error {
	args := m.Called(refreshTokenID)
	return args.Error(0)
}

func (m *MockAuthService) GetUserFromToken(token string) (*users.UserResponse, error) {
	args := m.Called(token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*users.UserResponse), args.Error(1)
}

// MockJWTService is a mock implementation of JWTServiceInterface
type MockJWTService struct {
	mock.Mock
}

func (m *MockJWTService) GenerateAccessToken(userID int, email, role string) (string, error) {
	args := m.Called(userID, email, role)
	return args.String(0), args.Error(1)
}

func (m *MockJWTService) ValidateAccessToken(token string) (*Claims, error) {
	args := m.Called(token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*Claims), args.Error(1)
}

func (m *MockJWTService) ExtractUserIDFromToken(token string) (int, error) {
	args := m.Called(token)
	return args.Int(0), args.Error(1)
}

// MockRedisService is a mock implementation of RedisServiceInterface
type MockRedisService struct {
	mock.Mock
}

func (m *MockRedisService) StoreRefreshToken(userID int, email, role string) (string, error) {
	args := m.Called(userID, email, role)
	return args.String(0), args.Error(1)
}

func (m *MockRedisService) GetRefreshToken(refreshTokenID string) (*RefreshTokenData, error) {
	args := m.Called(refreshTokenID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*RefreshTokenData), args.Error(1)
}

func (m *MockRedisService) DeleteRefreshToken(refreshTokenID string) error {
	args := m.Called(refreshTokenID)
	return args.Error(0)
}

func (m *MockRedisService) RotateRefreshToken(oldRefreshTokenID string, userID int, email, role string) (string, error) {
	args := m.Called(oldRefreshTokenID, userID, email, role)
	return args.String(0), args.Error(1)
}

// MockUserRepository is a mock implementation of users.RepositoryInterface
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(user *users.User) error {
	args := m.Called(user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByEmail(email string) (*users.User, error) {
	args := m.Called(email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*users.User), args.Error(1)
}

func (m *MockUserRepository) GetByID(id int) (*users.User, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*users.User), args.Error(1)
}

func (m *MockUserRepository) EmailExists(email string) (bool, error) {
	args := m.Called(email)
	return args.Bool(0), args.Error(1)
}
