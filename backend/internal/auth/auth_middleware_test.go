package auth

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestAuthMiddleware_Success(t *testing.T) {
	mockJWTService := &MockJWTService{}
	middleware := AuthMiddleware(mockJWTService)

	router := setupTestRouter()
	router.Use(middleware)
	router.GET("/protected", func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		assert.True(t, exists)
		assert.Equal(t, 1, userID)

		userEmail, exists := c.Get("user_email")
		assert.True(t, exists)
		assert.Equal(t, "<EMAIL>", userEmail)

		userRole, exists := c.Get("user_role")
		assert.True(t, exists)
		assert.Equal(t, "client", userRole)

		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	claims := &Claims{
		UserID: 1,
		Email:  "<EMAIL>",
		Role:   "client",
	}

	mockJWTService.On("ValidateAccessToken", "valid-token").Return(claims, nil)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer valid-token")

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	mockJWTService.AssertExpectations(t)
}

func TestAuthMiddleware_MissingToken(t *testing.T) {
	mockJWTService := &MockJWTService{}
	middleware := AuthMiddleware(mockJWTService)

	router := setupTestRouter()
	router.Use(middleware)
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/protected", nil)

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestAuthMiddleware_InvalidTokenFormat(t *testing.T) {
	mockJWTService := &MockJWTService{}
	middleware := AuthMiddleware(mockJWTService)

	router := setupTestRouter()
	router.Use(middleware)
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "InvalidFormat")

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestAuthMiddleware_InvalidToken(t *testing.T) {
	mockJWTService := &MockJWTService{}
	middleware := AuthMiddleware(mockJWTService)

	router := setupTestRouter()
	router.Use(middleware)
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	mockJWTService.On("ValidateAccessToken", "invalid-token").Return(nil, assert.AnError)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer invalid-token")

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)
	mockJWTService.AssertExpectations(t)
}

func TestRequireRole_Success(t *testing.T) {
	middleware := RequireRole("client")

	router := setupTestRouter()
	router.Use(func(c *gin.Context) {
		// Simulate auth middleware setting user context
		c.Set("user_role", "client")
		c.Next()
	})
	router.Use(middleware)
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/protected", nil)

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestRequireRole_WrongRole(t *testing.T) {
	middleware := RequireRole("admin")

	router := setupTestRouter()
	router.Use(func(c *gin.Context) {
		// Simulate auth middleware setting user context
		c.Set("user_role", "client")
		c.Next()
	})
	router.Use(middleware)
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/protected", nil)

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code)
}

func TestRequireRole_MissingRole(t *testing.T) {
	middleware := RequireRole("client")

	router := setupTestRouter()
	router.Use(middleware)
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/protected", nil)

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code)
}

func TestCORSMiddleware(t *testing.T) {
	middleware := CORSMiddleware()

	router := setupTestRouter()
	router.Use(middleware)
	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
	assert.Equal(t, "GET, POST, PUT, DELETE, OPTIONS", w.Header().Get("Access-Control-Allow-Methods"))
	assert.Equal(t, "Content-Type, Authorization", w.Header().Get("Access-Control-Allow-Headers"))
}

func TestCORSMiddleware_Preflight(t *testing.T) {
	middleware := CORSMiddleware()

	router := setupTestRouter()
	router.Use(middleware)
	router.OPTIONS("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("OPTIONS", "/test", nil)

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
}
