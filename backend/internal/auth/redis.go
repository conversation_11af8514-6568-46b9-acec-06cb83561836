package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

// RedisServiceInterface defines the interface for Redis operations
type RedisServiceInterface interface {
	StoreRefreshToken(userID int, email, role string) (string, error)
	GetRefreshToken(refreshTokenID string) (*RefreshTokenData, error)
	DeleteRefreshToken(refreshTokenID string) error
	RotateRefreshToken(oldRefreshTokenID string, userID int, email, role string) (string, error)
}

// RefreshTokenData represents the data stored for a refresh token
type RefreshTokenData struct {
	UserID    int       `json:"user_id"`
	Email     string    `json:"email"`
	Role      string    `json:"role"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
}

// RedisService handles Redis operations for session management
type RedisService struct {
	client          *redis.Client
	refreshTokenTTL time.Duration
}

// NewRedisService creates a new Redis service
func NewRedisService(client *redis.Client, refreshTokenTTL time.Duration) *RedisService {
	return &RedisService{
		client:          client,
		refreshTokenTTL: refreshTokenTTL,
	}
}

// StoreRefreshToken stores a refresh token in Redis
func (r *RedisService) StoreRefreshToken(userID int, email, role string) (string, error) {
	refreshTokenID := uuid.New().String()

	tokenData := RefreshTokenData{
		UserID:    userID,
		Email:     email,
		Role:      role,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(r.refreshTokenTTL),
	}

	data, err := json.Marshal(tokenData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal refresh token data: %w", err)
	}

	key := fmt.Sprintf("refresh_token:%s", refreshTokenID)
	err = r.client.Set(context.Background(), key, data, r.refreshTokenTTL).Err()
	if err != nil {
		return "", fmt.Errorf("failed to store refresh token: %w", err)
	}

	return refreshTokenID, nil
}

// GetRefreshToken retrieves refresh token data from Redis
func (r *RedisService) GetRefreshToken(refreshTokenID string) (*RefreshTokenData, error) {
	key := fmt.Sprintf("refresh_token:%s", refreshTokenID)

	data, err := r.client.Get(context.Background(), key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("refresh token not found or expired")
		}
		return nil, fmt.Errorf("failed to get refresh token: %w", err)
	}

	var tokenData RefreshTokenData
	err = json.Unmarshal([]byte(data), &tokenData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal refresh token data: %w", err)
	}

	// Check if token is expired
	if time.Now().After(tokenData.ExpiresAt) {
		// Delete expired token
		r.client.Del(context.Background(), key)
		return nil, fmt.Errorf("refresh token expired")
	}

	return &tokenData, nil
}

// DeleteRefreshToken removes a refresh token from Redis
func (r *RedisService) DeleteRefreshToken(refreshTokenID string) error {
	key := fmt.Sprintf("refresh_token:%s", refreshTokenID)

	err := r.client.Del(context.Background(), key).Err()
	if err != nil {
		return fmt.Errorf("failed to delete refresh token: %w", err)
	}

	return nil
}

// RotateRefreshToken creates a new refresh token and deletes the old one
func (r *RedisService) RotateRefreshToken(oldRefreshTokenID string, userID int, email, role string) (string, error) {
	// Create new refresh token
	newRefreshTokenID, err := r.StoreRefreshToken(userID, email, role)
	if err != nil {
		return "", err
	}

	// Delete old refresh token
	err = r.DeleteRefreshToken(oldRefreshTokenID)
	if err != nil {
		// If deletion fails, we should still return the new token
		// but log the error (in a real app, you'd use proper logging)
		fmt.Printf("Warning: failed to delete old refresh token: %v\n", err)
	}

	return newRefreshTokenID, nil
}
