package users

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestUser_ToResponse(t *testing.T) {
	now := time.Now()
	user := &User{
		ID:        1,
		Name:      "<PERSON>",
		Email:     "<EMAIL>",
		Password:  "hashed-password",
		Role:      "client",
		CreatedAt: now,
		UpdatedAt: now,
	}

	response := user.ToResponse()

	assert.Equal(t, user.ID, response.ID)
	assert.Equal(t, user.Name, response.Name)
	assert.Equal(t, user.Email, response.Email)
	assert.Equal(t, user.Role, response.Role)
	assert.Equal(t, user.CreatedAt, response.CreatedAt)
	assert.Equal(t, user.UpdatedAt, response.UpdatedAt)
	
	// Password should not be included in response
	// (This is ensured by the JSON tag "-" on the Password field)
}

func TestCreateUserRequest_Validation(t *testing.T) {
	tests := []struct {
		name    string
		request CreateUserRequest
		valid   bool
	}{
		{
			name: "valid client request",
			request: CreateUserRequest{
				Name:     "<PERSON>",
				Email:    "<EMAIL>",
				Password: "password123",
				Role:     "client",
			},
			valid: true,
		},
		{
			name: "valid chef request",
			request: CreateUserRequest{
				Name:     "<PERSON>",
				Email:    "<EMAIL>",
				Password: "password123",
				Role:     "chef",
			},
			valid: true,
		},
		{
			name: "empty name",
			request: CreateUserRequest{
				Name:     "",
				Email:    "<EMAIL>",
				Password: "password123",
				Role:     "client",
			},
			valid: false,
		},
		{
			name: "invalid email",
			request: CreateUserRequest{
				Name:     "John Doe",
				Email:    "invalid-email",
				Password: "password123",
				Role:     "client",
			},
			valid: false,
		},
		{
			name: "short password",
			request: CreateUserRequest{
				Name:     "John Doe",
				Email:    "<EMAIL>",
				Password: "short",
				Role:     "client",
			},
			valid: false,
		},
		{
			name: "invalid role",
			request: CreateUserRequest{
				Name:     "John Doe",
				Email:    "<EMAIL>",
				Password: "password123",
				Role:     "invalid",
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Note: In a real application, you would use a validator
			// like go-playground/validator to validate these structs
			// based on the binding tags. For now, we just test the structure.
			
			if tt.valid {
				assert.NotEmpty(t, tt.request.Name)
				assert.Contains(t, tt.request.Email, "@")
				assert.GreaterOrEqual(t, len(tt.request.Password), 8)
				assert.Contains(t, []string{"client", "chef"}, tt.request.Role)
			}
		})
	}
}

func TestLoginRequest_Structure(t *testing.T) {
	request := LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	assert.NotEmpty(t, request.Email)
	assert.NotEmpty(t, request.Password)
	assert.Contains(t, request.Email, "@")
}

func TestUserResponse_Structure(t *testing.T) {
	now := time.Now()
	response := UserResponse{
		ID:        1,
		Name:      "John Doe",
		Email:     "<EMAIL>",
		Role:      "client",
		CreatedAt: now,
		UpdatedAt: now,
	}

	assert.Equal(t, 1, response.ID)
	assert.Equal(t, "John Doe", response.Name)
	assert.Equal(t, "<EMAIL>", response.Email)
	assert.Equal(t, "client", response.Role)
	assert.Equal(t, now, response.CreatedAt)
	assert.Equal(t, now, response.UpdatedAt)
}
