# Mesaka Backend API

A Go-based REST API for the Mesaka chef-client marketplace platform, built with Gin framework.

## Features

- **Authentication System**: JWT-based authentication with refresh tokens
- **User Management**: User registration and profile management
- **Session Management**: Redis-based session storage with token rotation
- **Database**: PostgreSQL with connection pooling
- **Security**: Password hashing with bcrypt, CORS support
- **Configuration**: Environment-based configuration management

## Prerequisites

- Go 1.24.2 or later
- PostgreSQL 15+
- Redis 7+

## Quick Start

### 1. Setup Environment

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your database and Redis credentials.

### 2. Database Setup

Run the database migration:

```sql
-- Connect to your PostgreSQL database and run:
\i migrations/001_create_users_table.sql
```

### 3. Start Dependencies

Using Docker Compose (from project root):

```bash
docker-compose -f infra/docker-compose.yml up -d
```

### 4. Build and Run

```bash
# Build the server
go build -o bin/server ./cmd/server

# Run the server
./bin/server
```

Or run directly:

```bash
go run ./cmd/server
```

The server will start on `http://localhost:8080` by default.

## API Endpoints

### Authentication

- `POST /api/v1/auth/signup` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Get current user info (requires auth)

### Health Check

- `GET /health` - Server health status

## API Usage Examples

### Register a new user

```bash
curl -X POST http://localhost:8080/api/v1/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "securepassword123",
    "role": "client"
  }'
```

### Login

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### Get user info (with token)

```bash
curl -X GET http://localhost:8080/api/v1/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Configuration

The application uses environment variables for configuration. See `.env.example` for all available options.

Key configuration options:

- `JWT_SECRET`: Secret key for JWT signing (change in production!)
- `DB_*`: Database connection settings
- `REDIS_*`: Redis connection settings
- `SERVER_PORT`: Server port (default: 8080)

## Development

### Project Structure

```
backend/
├── cmd/server/          # Application entry point
├── internal/
│   ├── auth/           # Authentication logic
│   ├── users/          # User management
│   └── common/         # Shared utilities
├── pkg/db/             # Database utilities
├── migrations/         # Database migrations
└── bin/                # Built binaries
```

### Building

```bash
# Build for current platform
go build -o bin/server ./cmd/server

# Build for Linux
GOOS=linux GOARCH=amd64 go build -o bin/server-linux ./cmd/server
```

### Testing

```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...
```

## Security Notes

- Change `JWT_SECRET` to a secure random value in production
- Use HTTPS in production
- Configure proper CORS origins for your frontend
- Use strong database passwords
- Enable Redis authentication in production
