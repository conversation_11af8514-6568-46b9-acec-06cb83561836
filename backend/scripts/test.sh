#!/bin/bash

# Test script for Mesaka backend
set -e

echo "Running Mesaka Backend Tests"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

print_status "Go version: $(go version)"

# Run tests with coverage
echo ""
echo "Running unit tests..."
if go test ./... -v -coverprofile=coverage.out; then
    print_status "All tests passed!"
else
    print_error "Some tests failed!"
    exit 1
fi

# Generate coverage report
echo ""
echo "Generating coverage report..."
go tool cover -html=coverage.out -o coverage.html
print_status "Coverage report generated: coverage.html"

# Show coverage summary
echo ""
echo "Coverage Summary:"
echo "=================="
go tool cover -func=coverage.out | grep -E "(auth|users)" | head -10
echo ""
go tool cover -func=coverage.out | tail -1

# Check for race conditions (optional)
if [ "$1" = "--race" ]; then
    echo ""
    echo "Running race condition tests..."
    if go test ./... -race; then
        print_status "No race conditions detected!"
    else
        print_warning "Race conditions detected!"
    fi
fi

# Run benchmarks (optional)
if [ "$1" = "--bench" ]; then
    echo ""
    echo "Running benchmarks..."
    go test ./... -bench=. -benchmem
fi

echo ""
print_status "Test suite completed successfully!"
echo ""
echo "View detailed coverage report by opening: coverage.html"
echo "Ready to deploy!"
