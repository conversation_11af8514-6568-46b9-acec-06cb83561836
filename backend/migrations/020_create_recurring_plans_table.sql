-- Create recurring_plans table
-- One-to-one relationship with bookings for recurring booking plans

CREATE TABLE IF NOT EXISTS recurring_plans (
    id SERIAL PRIMARY KEY,
    booking_id INTEGER NOT NULL UNIQUE,
    chef_profile_id INTEGER NOT NULL,
    frequency VARCHAR(20) NOT NULL,
    occurrences INTEGER CHECK (occurrences > 0),
    next_occurrence DATE NOT NULL,
    recurring_price DECIMAL(12,2) NOT NULL CHECK (recurring_price >= 0),
    preferences TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_recurring_plans_booking_id 
        FOREIGN KEY (booking_id) 
        REFERENCES bookings(id) 
        ON DELETE CASCADE,
    CONSTRAINT fk_recurring_plans_chef_profile_id 
        FOREIGN KEY (chef_profile_id) 
        REFERENCES chef_profiles(id) 
        ON DELETE CASCADE,
    
    -- Ensure valid frequency values
    CONSTRAINT chk_recurring_plans_frequency 
        CHECK (frequency IN ('daily', 'weekly', 'bi_weekly', 'monthly', 'quarterly')),
    
    -- Ensure next occurrence is not in the past
    CONSTRAINT chk_recurring_plans_next_occurrence 
        CHECK (next_occurrence >= CURRENT_DATE)
);

-- Indexes for performance
CREATE UNIQUE INDEX IF NOT EXISTS idx_recurring_plans_booking_id ON recurring_plans(booking_id);
CREATE INDEX IF NOT EXISTS idx_recurring_plans_chef_profile_id ON recurring_plans(chef_profile_id);
CREATE INDEX IF NOT EXISTS idx_recurring_plans_frequency ON recurring_plans(frequency);
CREATE INDEX IF NOT EXISTS idx_recurring_plans_next_occurrence ON recurring_plans(next_occurrence);
CREATE INDEX IF NOT EXISTS idx_recurring_plans_created_at ON recurring_plans(created_at);

-- Composite index for chef's recurring plans
CREATE INDEX IF NOT EXISTS idx_recurring_plans_chef_next 
    ON recurring_plans(chef_profile_id, next_occurrence);

-- Index for upcoming recurring plans
CREATE INDEX IF NOT EXISTS idx_recurring_plans_upcoming 
    ON recurring_plans(next_occurrence) 
    WHERE next_occurrence >= CURRENT_DATE;
