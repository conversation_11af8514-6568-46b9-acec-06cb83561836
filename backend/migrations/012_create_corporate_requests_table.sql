-- Create corporate_requests table
-- One-to-many relationship with users (clients) for corporate event requests

CREATE TABLE IF NOT EXISTS corporate_requests (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    organization_name VARCHAR(255) NOT NULL,
    requirements TEXT,
    event_date DATE NOT NULL,
    expected_attendees INTEGER CHECK (expected_attendees > 0),
    budget_estimate DECIMAL(12,2) CHECK (budget_estimate >= 0),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_corporate_requests_client_id 
        FOREIGN KEY (client_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    
    -- Ensure required fields are not empty
    CONSTRAINT chk_corporate_requests_organization_not_empty 
        CHECK (LENGTH(TRIM(organization_name)) > 0),
    
    -- Ensure valid status values
    CONSTRAINT chk_corporate_requests_status 
        CHECK (status IN ('pending', 'reviewing', 'approved', 'rejected', 'converted')),
    
    -- Ensure event date is not in the past (allow same day)
    CONSTRAINT chk_corporate_requests_event_date 
        CHECK (event_date >= CURRENT_DATE)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_corporate_requests_client_id ON corporate_requests(client_id);
CREATE INDEX IF NOT EXISTS idx_corporate_requests_status ON corporate_requests(status);
CREATE INDEX IF NOT EXISTS idx_corporate_requests_event_date ON corporate_requests(event_date);
CREATE INDEX IF NOT EXISTS idx_corporate_requests_created_at ON corporate_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_corporate_requests_budget_estimate ON corporate_requests(budget_estimate);

-- Composite index for client's requests by status
CREATE INDEX IF NOT EXISTS idx_corporate_requests_client_status 
    ON corporate_requests(client_id, status, created_at DESC);

-- Index for pending requests by event date
CREATE INDEX IF NOT EXISTS idx_corporate_requests_pending_by_date 
    ON corporate_requests(event_date) 
    WHERE status = 'pending';
