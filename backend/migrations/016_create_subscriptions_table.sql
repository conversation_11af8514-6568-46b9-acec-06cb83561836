-- Create subscriptions table
-- One-to-many relationship with users for subscription plans

CREATE TABLE IF NOT EXISTS subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    monthly_price DECIMAL(10,2) NOT NULL CHECK (monthly_price >= 0),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    start_date DATE NOT NULL,
    end_date DATE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_subscriptions_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    
    -- Ensure valid subscription plans
    CONSTRAINT chk_subscriptions_plan_name 
        CHECK (plan_name IN ('basic', 'premium', 'professional', 'enterprise')),
    
    -- Ensure valid status values
    CONSTRAINT chk_subscriptions_status 
        CHECK (status IN ('active', 'cancelled', 'expired', 'suspended', 'trial')),
    
    -- Ensure end_date is after start_date if provided
    CONSTRAINT chk_subscriptions_dates 
        CHECK (end_date IS NULL OR end_date > start_date),
    
    -- Ensure plan name is not empty
    CONSTRAINT chk_subscriptions_plan_name_not_empty 
        CHECK (LENGTH(TRIM(plan_name)) > 0)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_plan_name ON subscriptions(plan_name);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_start_date ON subscriptions(start_date);
CREATE INDEX IF NOT EXISTS idx_subscriptions_end_date ON subscriptions(end_date);
CREATE INDEX IF NOT EXISTS idx_subscriptions_updated_at ON subscriptions(updated_at);

-- Composite index for user's active subscriptions
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_active 
    ON subscriptions(user_id, status, start_date DESC) 
    WHERE status = 'active';

-- Index for expiring subscriptions
CREATE INDEX IF NOT EXISTS idx_subscriptions_expiring 
    ON subscriptions(end_date) 
    WHERE status = 'active' AND end_date IS NOT NULL;
