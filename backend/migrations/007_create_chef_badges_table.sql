-- Create chef_badges table
-- Junction table for many-to-many relationship between chef_profiles and sustainability_badges

CREATE TABLE IF NOT EXISTS chef_badges (
    id SERIAL PRIMARY KEY,
    chef_profile_id INTEGER NOT NULL,
    badge_id INTEGER NOT NULL,
    awarded_at DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Foreign key constraints
    CONSTRAINT fk_chef_badges_chef_profile_id 
        FOREIGN KEY (chef_profile_id) 
        REFERENCES chef_profiles(id) 
        ON DELETE CASCADE,
    CONSTRAINT fk_chef_badges_badge_id 
        FOREIGN KEY (badge_id) 
        REFERENCES sustainability_badges(id) 
        ON DELETE CASCADE,
    
    -- Ensure a chef can't have the same badge multiple times
    CONSTRAINT uk_chef_badges_chef_badge 
        UNIQUE (chef_profile_id, badge_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_chef_badges_chef_profile_id ON chef_badges(chef_profile_id);
CREATE INDEX IF NOT EXISTS idx_chef_badges_badge_id ON chef_badges(badge_id);
CREATE INDEX IF NOT EXISTS idx_chef_badges_awarded_at ON chef_badges(awarded_at);

-- Composite index for finding chef's badges
CREATE INDEX IF NOT EXISTS idx_chef_badges_chef_awarded 
    ON chef_badges(chef_profile_id, awarded_at DESC);
