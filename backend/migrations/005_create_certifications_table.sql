-- Create certifications table
-- One-to-many relationship with chef_profiles for professional certifications

CREATE TABLE IF NOT EXISTS certifications (
    id SERIAL PRIMARY KEY,
    chef_profile_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    issuer VARCHAR(255) NOT NULL,
    issued_at DATE NOT NULL,
    expires_at DATE,
    document_url VARCHAR(500),
    
    -- Foreign key constraint
    CONSTRAINT fk_certifications_chef_profile_id 
        FOREIGN KEY (chef_profile_id) 
        REFERENCES chef_profiles(id) 
        ON DELETE CASCADE,
    
    -- Ensure title and issuer are not empty
    CONSTRAINT chk_certifications_title_not_empty 
        CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT chk_certifications_issuer_not_empty 
        CHECK (LENGTH(TRIM(issuer)) > 0),
    
    -- Ensure expiration date is after issue date if provided
    CONSTRAINT chk_certifications_dates 
        CHECK (expires_at IS NULL OR expires_at > issued_at)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_certifications_chef_profile_id ON certifications(chef_profile_id);
CREATE INDEX IF NOT EXISTS idx_certifications_issued_at ON certifications(issued_at);
CREATE INDEX IF NOT EXISTS idx_certifications_expires_at ON certifications(expires_at);

-- Index for finding active certifications
CREATE INDEX IF NOT EXISTS idx_certifications_active 
    ON certifications(chef_profile_id, expires_at) 
    WHERE expires_at IS NULL OR expires_at > CURRENT_DATE;
