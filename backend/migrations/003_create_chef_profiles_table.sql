-- Create chef_profiles table
-- One-to-one relationship with users table for chef-specific data

CREATE TABLE IF NOT EXISTS chef_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE,
    headline VARCHAR(255),
    bio TEXT,
    base_rate DECIMAL(10,2) CHECK (base_rate >= 0),
    cuisine_specialties TEXT,
    rating_avg REAL DEFAULT 0.0 CHECK (rating_avg >= 0 AND rating_avg <= 5),
    rating_count INTEGER DEFAULT 0 CHECK (rating_count >= 0),
    verified BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_chef_profiles_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_chef_profiles_user_id ON chef_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_chef_profiles_verified ON chef_profiles(verified);
CREATE INDEX IF NOT EXISTS idx_chef_profiles_rating_avg ON chef_profiles(rating_avg);
CREATE INDEX IF NOT EXISTS idx_chef_profiles_base_rate ON chef_profiles(base_rate);

-- Partial index for verified chefs with good ratings
CREATE INDEX IF NOT EXISTS idx_chef_profiles_verified_rated 
    ON chef_profiles(rating_avg DESC) 
    WHERE verified = true AND rating_count > 0;
