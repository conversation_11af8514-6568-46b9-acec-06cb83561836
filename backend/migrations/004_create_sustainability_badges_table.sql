-- Create sustainability_badges table
-- Reference table for sustainability badges that can be awarded to chefs

CREATE TABLE IF NOT EXISTS sustainability_badges (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Ensure code and name are not empty
    CONSTRAINT chk_sustainability_badges_code_not_empty 
        CHECK (LENGTH(TRIM(code)) > 0),
    CONSTRAINT chk_sustainability_badges_name_not_empty 
        CHECK (LENGTH(TRIM(name)) > 0)
);

-- Indexes for performance
CREATE UNIQUE INDEX IF NOT EXISTS idx_sustainability_badges_code ON sustainability_badges(code);
CREATE INDEX IF NOT EXISTS idx_sustainability_badges_name ON sustainability_badges(name);

-- Insert some common sustainability badges
INSERT INTO sustainability_badges (code, name, description) VALUES
    ('LOCAL_SOURCING', 'Local Sourcing Champion', 'Sources ingredients from local farms and producers within 100 miles'),
    ('ORGANIC_CERTIFIED', 'Organic Certified', 'Certified to use organic ingredients and sustainable cooking practices'),
    ('ZERO_WASTE', 'Zero Waste Kitchen', 'Implements comprehensive waste reduction and composting practices'),
    ('PLANT_FORWARD', 'Plant-Forward Menu', 'Specializes in plant-based and vegetarian cuisine options'),
    ('SUSTAINABLE_SEAFOOD', 'Sustainable Seafood', 'Uses only sustainably sourced and certified seafood'),
    ('ENERGY_EFFICIENT', 'Energy Efficient Operations', 'Uses energy-efficient equipment and renewable energy sources')
ON CONFLICT (code) DO NOTHING;
