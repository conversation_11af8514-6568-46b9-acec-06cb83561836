-- Create transactions table
-- One-to-many relationship with payments for detailed transaction records

CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY,
    payment_id INTEGER NOT NULL,
    type VARCHAR(20) NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_transactions_payment_id 
        FOREIGN KEY (payment_id) 
        REFERENCES payments(id) 
        ON DELETE CASCADE,
    
    -- Ensure valid transaction types
    CONSTRAINT chk_transactions_type 
        CHECK (type IN ('charge', 'refund', 'partial_refund', 'chargeback', 'fee', 'payout')),
    
    -- Ensure valid currency codes (ISO 4217)
    CONSTRAINT chk_transactions_currency 
        CHECK (LENGTH(currency) = 3 AND currency = UPPER(currency)),
    
    -- Ensure valid status values
    CONSTRAINT chk_transactions_status 
        CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    
    -- Amount can be negative for refunds, but not zero
    CONSTRAINT chk_transactions_amount_not_zero 
        CHECK (amount != 0)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_transactions_payment_id ON transactions(payment_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- Composite index for payment transactions by type and status
CREATE INDEX IF NOT EXISTS idx_transactions_payment_type_status 
    ON transactions(payment_id, type, status, created_at DESC);

-- Index for completed transactions by type
CREATE INDEX IF NOT EXISTS idx_transactions_completed_by_type 
    ON transactions(type, created_at DESC) 
    WHERE status = 'completed';
