-- Create payments table
-- One-to-many relationship with bookings for payment records

CREATE TABLE IF NOT EXISTS payments (
    id SERIAL PRIMARY KEY,
    booking_id INTEGER NOT NULL,
    provider VARCHAR(50) NOT NULL,
    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    paid_at TIMESTAMP WITH TIME ZONE,
    transaction_reference VARCHAR(255),
    
    -- Foreign key constraint
    CONSTRAINT fk_payments_booking_id 
        FOREIGN KEY (booking_id) 
        REFERENCES bookings(id) 
        ON DELETE CASCADE,
    
    -- Ensure valid payment providers
    CONSTRAINT chk_payments_provider 
        CHECK (provider IN ('stripe', 'paypal', 'square', 'bank_transfer', 'cash')),
    
    -- Ensure valid currency codes (ISO 4217)
    CONSTRAINT chk_payments_currency 
        CHECK (LENGTH(currency) = 3 AND currency = UPPER(currency)),
    
    -- Ensure valid status values
    CONSTRAINT chk_payments_status 
        CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
    
    -- Ensure provider is not empty
    CONSTRAINT chk_payments_provider_not_empty 
        CHECK (LENGTH(TRIM(provider)) > 0),
    
    -- If status is completed, paid_at must be set
    CONSTRAINT chk_payments_completed_has_paid_at 
        CHECK (status != 'completed' OR paid_at IS NOT NULL)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_payments_booking_id ON payments(booking_id);
CREATE INDEX IF NOT EXISTS idx_payments_provider ON payments(provider);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_paid_at ON payments(paid_at);
CREATE INDEX IF NOT EXISTS idx_payments_transaction_reference ON payments(transaction_reference);

-- Composite index for booking payments by status
CREATE INDEX IF NOT EXISTS idx_payments_booking_status 
    ON payments(booking_id, status, paid_at DESC);

-- Index for completed payments
CREATE INDEX IF NOT EXISTS idx_payments_completed 
    ON payments(paid_at DESC) 
    WHERE status = 'completed';
