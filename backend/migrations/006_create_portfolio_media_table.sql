-- Create portfolio_media table
-- One-to-many relationship with chef_profiles for portfolio images and videos

CREATE TABLE IF NOT EXISTS portfolio_media (
    id SERIAL PRIMARY KEY,
    chef_profile_id INTEGER NOT NULL,
    media_type VARCHAR(20) NOT NULL,
    url VARCHAR(500) NOT NULL,
    caption TEXT,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_portfolio_media_chef_profile_id 
        FOREIGN KEY (chef_profile_id) 
        REFERENCES chef_profiles(id) 
        ON DELETE CASCADE,
    
    -- Ensure media_type is valid
    CONSTRAINT chk_portfolio_media_type 
        CHECK (media_type IN ('image', 'video')),
    
    -- Ensure URL is not empty
    CONSTRAINT chk_portfolio_media_url_not_empty 
        CHECK (LENGTH(TRIM(url)) > 0)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_portfolio_media_chef_profile_id ON portfolio_media(chef_profile_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_media_type ON portfolio_media(media_type);
CREATE INDEX IF NOT EXISTS idx_portfolio_media_uploaded_at ON portfolio_media(uploaded_at);

-- Composite index for chef's media ordered by upload date
CREATE INDEX IF NOT EXISTS idx_portfolio_media_chef_uploaded 
    ON portfolio_media(chef_profile_id, uploaded_at DESC);
