-- Create recommendations table
-- Many-to-many relationship between users and chef_profiles for ML recommendations

CREATE TABLE IF NOT EXISTS recommendations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    chef_profile_id INTEGER NOT NULL,
    score REAL NOT NULL,
    reason TEXT,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_recommendations_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    CONSTRAINT fk_recommendations_chef_profile_id 
        FOREIGN KEY (chef_profile_id) 
        REFERENCES chef_profiles(id) 
        ON DELETE CASCADE,
    
    -- Ensure score is between 0 and 1 (probability/confidence score)
    CONSTRAINT chk_recommendations_score_range 
        CHECK (score >= 0 AND score <= 1),
    
    -- Ensure unique recommendation per user-chef pair (can be updated)
    CONSTRAINT uk_recommendations_user_chef 
        UNIQUE (user_id, chef_profile_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_recommendations_user_id ON recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_chef_profile_id ON recommendations(chef_profile_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_score ON recommendations(score);
CREATE INDEX IF NOT EXISTS idx_recommendations_generated_at ON recommendations(generated_at);

-- Composite index for user's recommendations ordered by score
CREATE INDEX IF NOT EXISTS idx_recommendations_user_score 
    ON recommendations(user_id, score DESC, generated_at DESC);

-- Index for high-score recommendations
CREATE INDEX IF NOT EXISTS idx_recommendations_high_score 
    ON recommendations(user_id, generated_at DESC) 
    WHERE score >= 0.7;

-- Index for chef's recommendations
CREATE INDEX IF NOT EXISTS idx_recommendations_chef_score 
    ON recommendations(chef_profile_id, score DESC, generated_at DESC);
