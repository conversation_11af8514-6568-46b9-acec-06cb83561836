-- Create reviews table
-- One-to-one relationship with bookings for booking reviews

CREATE TABLE IF NOT EXISTS reviews (
    id SERIAL PRIMARY KEY,
    booking_id INTEGER NOT NULL UNIQUE,
    reviewer_id INTEGER NOT NULL,
    chef_profile_id INTEGER NOT NULL,
    rating INTEGER NOT NULL,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_reviews_booking_id 
        FOREIGN KEY (booking_id) 
        REFERENCES bookings(id) 
        ON DELETE CASCADE,
    CONSTRAINT fk_reviews_reviewer_id 
        FOREIGN KEY (reviewer_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    CONSTRAINT fk_reviews_chef_profile_id 
        FOREIGN KEY (chef_profile_id) 
        REFERENCES chef_profiles(id) 
        ON DELETE CASCADE,
    
    -- Ensure rating is between 1 and 5
    CONSTRAINT chk_reviews_rating_range 
        CHECK (rating >= 1 AND rating <= 5)
);

-- Indexes for performance
CREATE UNIQUE INDEX IF NOT EXISTS idx_reviews_booking_id ON reviews(booking_id);
CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_id ON reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_reviews_chef_profile_id ON reviews(chef_profile_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating);
CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at);

-- Composite index for chef's reviews ordered by date
CREATE INDEX IF NOT EXISTS idx_reviews_chef_created 
    ON reviews(chef_profile_id, created_at DESC);

-- Index for high-rated reviews
CREATE INDEX IF NOT EXISTS idx_reviews_high_rated 
    ON reviews(chef_profile_id, created_at DESC) 
    WHERE rating >= 4;

-- Index for reviewer's reviews
CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_created 
    ON reviews(reviewer_id, created_at DESC);
