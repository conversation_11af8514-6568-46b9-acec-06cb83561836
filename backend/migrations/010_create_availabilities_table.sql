-- Create availabilities table
-- One-to-many relationship with chef_profiles for chef availability rules

CREATE TABLE IF NOT EXISTS availabilities (
    id SERIAL PRIMARY KEY,
    chef_profile_id INTEGER NOT NULL,
    rule VARCHAR(100) NOT NULL,
    start_date DATE,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_availabilities_chef_profile_id 
        FOREIGN KEY (chef_profile_id) 
        REFERENCES chef_profiles(id) 
        ON DELETE CASCADE,
    
    -- Ensure rule is not empty
    CONSTRAINT chk_availabilities_rule_not_empty 
        CHECK (LENGTH(TRIM(rule)) > 0),
    
    -- Ensure end_date is after start_date if both provided
    CONSTRAINT chk_availabilities_dates 
        CHECK (start_date IS NULL OR end_date IS NULL OR end_date >= start_date),
    
    -- Ensure end_time is after start_time if both provided
    CONSTRAINT chk_availabilities_times 
        CHECK (start_time IS NULL OR end_time IS NULL OR end_time > start_time),
    
    -- Ensure timezone is not empty
    CONSTRAINT chk_availabilities_timezone_not_empty 
        CHECK (LENGTH(TRIM(timezone)) > 0)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_availabilities_chef_profile_id ON availabilities(chef_profile_id);
CREATE INDEX IF NOT EXISTS idx_availabilities_start_date ON availabilities(start_date);
CREATE INDEX IF NOT EXISTS idx_availabilities_end_date ON availabilities(end_date);
CREATE INDEX IF NOT EXISTS idx_availabilities_created_at ON availabilities(created_at);

-- Composite index for date range queries
CREATE INDEX IF NOT EXISTS idx_availabilities_date_range 
    ON availabilities(chef_profile_id, start_date, end_date);

-- Index for finding current availability rules
CREATE INDEX IF NOT EXISTS idx_availabilities_current 
    ON availabilities(chef_profile_id) 
    WHERE (start_date IS NULL OR start_date <= CURRENT_DATE) 
    AND (end_date IS NULL OR end_date >= CURRENT_DATE);
