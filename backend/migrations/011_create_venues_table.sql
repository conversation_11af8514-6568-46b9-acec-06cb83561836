-- Create venues table
-- One-to-many relationship with users (clients) for their venues

CREATE TABLE IF NOT EXISTS venues (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_venues_client_id 
        FOREIGN KEY (client_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    
    -- Ensure required fields are not empty
    CONSTRAINT chk_venues_name_not_empty 
        CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT chk_venues_address_not_empty 
        CHECK (LENGTH(TRIM(address)) > 0),
    CONSTRAINT chk_venues_city_not_empty 
        CHECK (LENGTH(TRIM(city)) > 0),
    CONSTRAINT chk_venues_country_not_empty 
        CHECK (LENGTH(TRIM(country)) > 0),
    
    -- Ensure valid latitude and longitude ranges
    CONSTRAINT chk_venues_latitude_range 
        CHECK (latitude IS NULL OR (latitude >= -90 AND latitude <= 90)),
    CONSTRAINT chk_venues_longitude_range 
        CHECK (longitude IS NULL OR (longitude >= -180 AND longitude <= 180))
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_venues_client_id ON venues(client_id);
CREATE INDEX IF NOT EXISTS idx_venues_city ON venues(city);
CREATE INDEX IF NOT EXISTS idx_venues_country ON venues(country);
CREATE INDEX IF NOT EXISTS idx_venues_created_at ON venues(created_at);

-- Spatial index for location-based queries (if both lat/lng are present)
CREATE INDEX IF NOT EXISTS idx_venues_location 
    ON venues(latitude, longitude) 
    WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Composite index for client's venues
CREATE INDEX IF NOT EXISTS idx_venues_client_created 
    ON venues(client_id, created_at DESC);
