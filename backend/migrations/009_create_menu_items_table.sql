-- Create menu_items table
-- One-to-many relationship with menus for individual menu items

CREATE TABLE IF NOT EXISTS menu_items (
    id SERIAL PRIMARY KEY,
    menu_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) CHECK (price >= 0),
    dietary_tags TEXT,
    is_vegetarian BOOLEAN NOT NULL DEFAULT false,
    is_gluten_free BOOLEAN NOT NULL DEFAULT false,
    
    -- Foreign key constraint
    CONSTRAINT fk_menu_items_menu_id 
        FOREIGN KEY (menu_id) 
        REFERENCES menus(id) 
        ON DELETE CASCADE,
    
    -- Ensure name is not empty
    CONSTRAINT chk_menu_items_name_not_empty 
        CHECK (LENGTH(TRIM(name)) > 0)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_menu_items_menu_id ON menu_items(menu_id);
CREATE INDEX IF NOT EXISTS idx_menu_items_is_vegetarian ON menu_items(is_vegetarian);
CREATE INDEX IF NOT EXISTS idx_menu_items_is_gluten_free ON menu_items(is_gluten_free);
CREATE INDEX IF NOT EXISTS idx_menu_items_price ON menu_items(price);

-- Composite indexes for dietary filtering
CREATE INDEX IF NOT EXISTS idx_menu_items_dietary 
    ON menu_items(menu_id, is_vegetarian, is_gluten_free);

-- Index for vegetarian items across all menus
CREATE INDEX IF NOT EXISTS idx_menu_items_vegetarian 
    ON menu_items(menu_id) 
    WHERE is_vegetarian = true;

-- Index for gluten-free items across all menus
CREATE INDEX IF NOT EXISTS idx_menu_items_gluten_free 
    ON menu_items(menu_id) 
    WHERE is_gluten_free = true;
