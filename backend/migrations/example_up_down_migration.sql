-- Migration: 016_create_subscriptions_table
-- Description: Create subscriptions table for user subscription plans

-- +migrate Up
-- Create subscriptions table
-- One-to-many relationship with users for subscription plans

CREATE TABLE subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    monthly_price DECIMAL(10,2) NOT NULL CHECK (monthly_price >= 0),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    start_date DATE NOT NULL,
    end_date DATE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_subscriptions_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE,
    
    -- Ensure valid subscription plans
    CONSTRAINT chk_subscriptions_plan_name 
        CHECK (plan_name IN ('basic', 'premium', 'professional', 'enterprise')),
    
    -- Ensure valid status values
    CONSTRAINT chk_subscriptions_status 
        CHECK (status IN ('active', 'cancelled', 'expired', 'suspended', 'trial')),
    
    -- Ensure end_date is after start_date if provided
    CONSTRAINT chk_subscriptions_dates 
        CHECK (end_date IS NULL OR end_date > start_date),
    
    -- Ensure plan name is not empty
    CONSTRAINT chk_subscriptions_plan_name_not_empty 
        CHECK (LENGTH(TRIM(plan_name)) > 0)
);

-- Indexes for performance
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_plan_name ON subscriptions(plan_name);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_start_date ON subscriptions(start_date);
CREATE INDEX idx_subscriptions_end_date ON subscriptions(end_date);
CREATE INDEX idx_subscriptions_updated_at ON subscriptions(updated_at);

-- Composite index for user's active subscriptions
CREATE INDEX idx_subscriptions_user_active 
    ON subscriptions(user_id, status, start_date DESC) 
    WHERE status = 'active';

-- Index for expiring subscriptions
CREATE INDEX idx_subscriptions_expiring 
    ON subscriptions(end_date) 
    WHERE status = 'active' AND end_date IS NOT NULL;

-- +migrate Down
-- Remove subscriptions table and all associated indexes

-- Drop indexes first (they'll be dropped automatically with the table, but explicit is better)
DROP INDEX IF EXISTS idx_subscriptions_expiring;
DROP INDEX IF EXISTS idx_subscriptions_user_active;
DROP INDEX IF EXISTS idx_subscriptions_updated_at;
DROP INDEX IF EXISTS idx_subscriptions_end_date;
DROP INDEX IF EXISTS idx_subscriptions_start_date;
DROP INDEX IF EXISTS idx_subscriptions_status;
DROP INDEX IF EXISTS idx_subscriptions_plan_name;
DROP INDEX IF EXISTS idx_subscriptions_user_id;

-- Drop the table (this will also drop all constraints and indexes)
DROP TABLE IF EXISTS subscriptions;
