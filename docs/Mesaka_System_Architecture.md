# Mesaka – System Architecture (Production-ready, Scalable)

## Overview
Mesaka is a chef–client marketplace built for scale and reliability. This architecture uses a Next.js frontend, a set of backend microservices (Go preferred), PostgreSQL for durable relational data, Redis for caching and session/state, and event-driven components for scalable async work (queue). It supports multiple payment providers including Stripe/PayPal and **M-Pesa** (East Africa).

Goals:
- Horizontal scalability (stateless frontends & backends)
- Secure, robust authentication with token revocation
- Low-latency read operations via caching
- Resilient payment flows and idempotent webhook handling
- Observability and SRE best practices

---

## High-level Components

### Frontend (Next.js)
- **Technology**: Next.js (App Router), React, TypeScript.
- **Mode**:
  - Server-side rendering (SSR) for SEO-critical pages (chef discovery, public profiles).
  - Incremental Static Regeneration (ISR) for content-heavy pages (chef portfolios).
  - Client-side rendering (CSR) for interactive flows (booking wizard, chat UI).
- **Hosting**: Vercel / CloudFront + S3 for static assets (or self-host on Cloud run).
- **Edge**: Use edge middleware or edge functions for geolocation, A/B testing and caching headers.

Responsibilities:
- UI, forms, client validations, OAuth/OIDC redirects
- Minimal business logic; use backend APIs for authoritative actions
- Call backend APIs (REST/GraphQL) or use Next.js server-side API routes as BFF layer if desired

---

### Backend (Stateless Services)
- **Language**: Go (recommended) or Node/TypeScript microservices
- **Pattern**: Microservices or modular services behind API gateway
- **Services**:
  - Auth Service (OIDC/JWT + session management)
  - User/Chef Service (profiles, portfolios)
  - Booking Service (booking lifecycle, availability)
  - Menu Service (menus & menu_items)
  - Payment Service (Stripe/PayPal, M-Pesa adapters)
  - Messaging Service (WebSocket / socket svc)
  - Recommendation Service (rules engine → ML)
  - Notification Service (email, push)
  - Billing & Accounting Service (transactions, payouts)

Deployment:
- Docker containers orchestrated by Kubernetes (EKS/GKE/AKS) or AWS ECS/Fargate
- Services are stateless; persistent state in managed DB and caches

---

### Data Stores
- **Primary relational DB**: PostgreSQL (AWS RDS / Azure Database for PostgreSQL)
  - Master + read replicas for read scalability
  - Connection pooling: pgbouncer / proxy to prevent connection storms
  - Backups: automated snapshots and point-in-time recovery
- **Cache & Session Store**: Redis (AWS ElastiCache or self-hosted)
  - Caching layer for read-heavy endpoints (chef search results, profile fragments)
  - Session store for refresh token / short-lived session lookup and revocation lists
  - Rate-limiting counters
- **Object Storage**: AWS S3 (media, certifications)
  - Signed URLs for uploads (direct-from-client)
- **Message Broker / Queue**: AWS SQS / RabbitMQ / Kafka
  - Async tasks (email, video processing, payment reconciliation, chef payout jobs)
- **Search**: ElasticSearch / OpenSearch
  - Full-text search and faceted filtering for chefs and menus

---

### Authentication & Authorization (Secure & Scalable)
Options (pick one):
- **Managed OIDC provider** (Auth0, Okta) — fastest, secure off-the-shelf with OIDC/JWKS
- **Self-hosted** (Keycloak) — full control
- **Custom** (recommended hybrid): auth microservice issuing JWTs with Redis-backed refresh tokens for revocation

Recommended production approach (Custom + standards):
- Short-lived **access tokens** (JWT, e.g. 5–15m) for API access
- **Refresh tokens** stored server-side in Redis with TTL and revocation list. Refresh tokens are opaque IDs mapping to refresh token metadata (user id, device, expiry).
- Support **device sessions** (user can revoke sessions).
- Use **PKCE** for public clients (mobile/SPA).
- Password storage: Argon2 or bcrypt + adaptive cost.
- 2FA/MFA support via TOTP or SMS (opt-in).
- RBAC or attribute-based access control for admin/chef/client scopes.

Revocation & rotation:
- Keep `jti`/token IDs and an allowlist/denylist in Redis to support immediate revocation.
- Rotate signing keys using a JWKS endpoint and key rotation process.

Rate-limiting & abuse protection:
- Use Redis-based sliding window counters per IP / per user.
- Apply stricter limits on auth endpoints.

---

### Caching Strategy (Redis)
- **Entity caches**: cache chef profiles, menu lists, and computed aggregates (rating average). TTL: 5–30 minutes depending on freshness needs.
- **Query result cache**: cache common search queries (with query signature key) and invalidate on relevant writes.
- **Session & refresh tokens**: store refresh token metadata in Redis
- **Locks & idempotency**: use Redis distributed locks for critical operations (e.g., single acceptance of a booking) and idempotency keys for payment/webhook handling.
- **Rate limiting**: per-user and per-IP counters.

Cache invalidation:
- Write-through where necessary (invalidate on profile update, menu change, new review)
- Use message broker events to propagate invalidations across nodes.

---

### Payments (Stripe, PayPal, M-Pesa)
- **Design**:
  - Payment Service handles provider-agnostic payment lifecycle with adapter modules for each provider.
  - Record external provider transaction IDs and payment status in DB.
  - Idempotent processing of webhooks.
- **M-Pesa Integration**:
  - Support M-Pesa flows: STK Push (customer prompt), C2B (customer to business), B2C (payouts to chefs).
  - Securely store API credentials and use rotating secrets; ensure signature verification for callbacks.
- **PCI**:
  - Offload card handling to Stripe/PayPal (no card data on your servers).
  - For M-Pesa, follow provider guidelines for handling phone numbers and callbacks.
- **Payouts**:
  - Payout service aggregates completed payments then triggers platform payouts (B2C) via provider adapters.

---

### Real-time Messaging (Chat)
- WebSocket service (stateless): use socket servers behind load balancer and sticky sessions OR session via token and persistent store (Redis) for presence.
- Alternatively use managed solutions (Pusher, Ably) for faster implementation.
- Persist messages in DB for history and attachments in S3.

---

### Observability & Ops
- **Metrics**: Prometheus (app metrics), Grafana dashboards (latency, error rates, queue depth)
- **Tracing**: OpenTelemetry / Jaeger (request traces across services)
- **Logging**: Structured logs (JSON) to ELK (Elasticsearch / OpenSearch + Kibana)
- **Error monitoring**: Sentry
- **Health checks**: readiness & liveness endpoints for Kubernetes

---

### CI/CD & IaC
- **CI**: GitHub Actions or GitLab CI to run tests, lint, build containers
- **CD**: Canary deployments via Kubernetes or ECS; blue/green for zero-downtime
- **Infrastructure**: Terraform for reproducible infra (VPCs, clusters, RDS, ElastiCache)
- **Secrets**: AWS Secrets Manager / HashiCorp Vault

---

### Security Best Practices
- TLS everywhere, HSTS, CSP, secure cookies
- Least-privilege IAM roles
- Parameterized SQL queries (ORM or query builder)
- WAF in front of API gateway
- Regular dependency vulnerability scanning
- Automated backups and disaster recovery plan

---

## Typical Request Flow (Booking + Payment)
1. Client chooses chef & menu in Next.js UI.
2. Frontend calls Booking Service `POST /bookings` (server-side or via BFF).
3. Booking created in DB with `pending` status. Cache invalidated if needed.
4. Payment Service returns provider checkout (Stripe session or triggers M-Pesa STK push).
5. Payment provider sends webhook → Payment Service verifies, marks payment `paid`.
6. Booking Service transitions to `confirmed`, notification sent (push/email), and chef receives assignment.
7. After service, client posts review → review stored; chef rating recomputed (background job) and cached.

---

## Folder & Deployment Map (suggested)
- `frontend/mesaka-next/` → Next.js app
- `services/auth/`
- `services/users/`
- `services/bookings/`
- `services/payments/`
- `services/messaging/`
- `infra/terraform/`
- `k8s/` manifests / helm charts
- `docs/` → architecture & API docs

---

## Production Checklist (short)
- Managed DB with replicas & backups
- Redis cluster for caching/sessions
- Idempotent webhook handling and retries
- Key rotation for JWTs
- Monitoring & alerting configured
- CI/CD pipelines and IaC configured
