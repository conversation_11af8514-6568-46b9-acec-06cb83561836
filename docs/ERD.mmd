erDiagram
    USERS {
      int id PK
      string name
      string email
      string password_hash
      string role
      string phone
      datetime created_at
      datetime updated_at
      boolean is_active
    }

    CHEF_PROFILES {
      int id PK
      int user_id FK
      string headline
      string bio
      decimal base_rate
      string cuisine_specialties
      float rating_avg
      int rating_count
      boolean verified
      datetime created_at
      datetime updated_at
    }

    CERTIFICATIONS {
      int id PK
      int chef_profile_id FK
      string title
      string issuer
      date issued_at
      date expires_at
      string document_url
    }

    PORTFOLIO_MEDIA {
      int id PK
      int chef_profile_id FK
      string media_type
      string url
      string caption
      datetime uploaded_at
    }

    SUSTAINABILITY_BADGES {
      int id PK
      string code
      string name
      string description
    }

    CHEF_BADGES {
      int id PK
      int chef_profile_id FK
      int badge_id FK
      date awarded_at
    }

    MENUS {
      int id PK
      int chef_profile_id FK
      string title
      string description
      boolean is_customizable
      decimal base_price
      datetime created_at
      datetime updated_at
    }

    MENU_ITEMS {
      int id PK
      int menu_id FK
      string name
      string description
      decimal price
      string dietary_tags
      boolean is_vegetarian
      boolean is_gluten_free
    }

    BOOKINGS {
      int id PK
      int client_id FK
      int chef_profile_id FK
      string booking_type
      date event_date
      time start_time
      time end_time
      string requirements
      decimal total_price
      string status
      datetime created_at
      datetime updated_at
    }

    PAYMENTS {
      int id PK
      int booking_id FK
      string provider
      decimal amount
      string currency
      string status
      datetime paid_at
      string transaction_reference
    }

    REVIEWS {
      int id PK
      int booking_id FK
      int reviewer_id FK
      int chef_profile_id FK
      int rating
      string comment
      datetime created_at
    }

    CONVERSATIONS {
      int id PK
      int booking_id FK
      datetime created_at
      datetime updated_at
    }

    MESSAGES {
      int id PK
      int conversation_id FK
      int sender_id FK
      string body
      string attachments
      datetime sent_at
      boolean read
    }

    AVAILABILITIES {
      int id PK
      int chef_profile_id FK
      string rule
      date start_date
      date end_date
      time start_time
      time end_time
      string timezone
      string notes
      datetime created_at
    }

    RECURRING_PLANS {
      int id PK
      int booking_id FK
      int chef_profile_id FK
      string frequency
      int occurrences
      date next_occurrence
      decimal recurring_price
      string preferences
      datetime created_at
      datetime updated_at
    }

    VENUES {
      int id PK
      int client_id FK
      string name
      string address
      string city
      string country
      decimal latitude
      decimal longitude
      string notes
      datetime created_at
    }

    CORPORATE_REQUESTS {
      int id PK
      int client_id FK
      string organization_name
      string requirements
      date event_date
      int expected_attendees
      decimal budget_estimate
      string status
      datetime created_at
      datetime updated_at
    }

    TRANSACTIONS {
      int id PK
      int payment_id FK
      string type
      decimal amount
      string currency
      datetime created_at
      string status
    }

    SUBSCRIPTIONS {
      int id PK
      int user_id FK
      string plan_name
      decimal monthly_price
      string status
      date start_date
      date end_date
      datetime updated_at
    }

    RECOMMENDATIONS {
      int id PK
      int user_id FK
      int chef_profile_id FK
      float score
      string reason
      datetime generated_at
    }

    %% --- RELATIONSHIPS ---
    USERS ||--o{ CHEF_PROFILES : has
    USERS ||--o{ BOOKINGS : places
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ SUBSCRIPTIONS : has
    CHEF_PROFILES ||--o{ CERTIFICATIONS : owns
    CHEF_PROFILES ||--o{ PORTFOLIO_MEDIA : uploads
    CHEF_PROFILES ||--o{ MENUS : creates
    MENUS ||--o{ MENU_ITEMS : contains
    CHEF_PROFILES ||--o{ AVAILABILITIES : sets
    CHEF_PROFILES ||--o{ CHEF_BADGES : earns
    SUSTAINABILITY_BADGES ||--o{ CHEF_BADGES : assigned
    BOOKINGS ||--o{ PAYMENTS : has
    BOOKINGS ||--o{ REVIEWS : generates
    BOOKINGS ||--o{ CONVERSATIONS : may_have
    CONVERSATIONS ||--o{ MESSAGES : contains
    BOOKINGS }|..|{ CHEF_PROFILES : involves
    BOOKINGS }|..|{ USERS : requested_by
    RECURRING_PLANS }|--|| BOOKINGS : extends
    VENUES ||--o{ BOOKINGS : hosts
    CORPORATE_REQUESTS ||--o{ BOOKINGS : converts_to
    PAYMENTS ||--o{ TRANSACTIONS : recorded_as
    USERS ||--o{ RECOMMENDATIONS : receives
    CHEF_PROFILES ||--o{ RECOMMENDATIONS : appears_in
