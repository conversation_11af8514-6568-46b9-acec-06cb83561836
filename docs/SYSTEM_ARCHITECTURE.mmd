flowchart LR
    subgraph ClientSide[Frontend]
        A[Next.js Web App]
    end

    subgraph APIGateway[API Gateway / Load Balancer]
        B[API Gateway]
    end

    subgraph BackendServices[Backend Services - Go]
        C1[Auth Service]
        C2[User & Chef Service]
        C3[Booking Service]
        C4[Payment Service]
        C5[Messaging Service]
    end

    subgraph DataStores[Data Layer]
        D1[(Postgres Database)]
        D2[(Redis Cache)]
    end

    subgraph ExternalIntegrations[External Services]
        E1[Stripe]
        E2[PayPal]
        E3[M-Pesa]
    end

    %% Connections
    A --> B
    B --> C1
    B --> C2
    B --> C3
    B --> C4
    B --> C5

    %% Service to DB
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1

    %% Redis caching
    C2 --> D2
    C3 --> D2
    C4 --> D2

    %% Payments
    C4 --> E1
    C4 --> E2
    C4 --> E3