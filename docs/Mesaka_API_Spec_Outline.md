# Mesaka – API Specification Outline (Next.js frontend, M-Pesa, Redis sessions)

## Principles
- RESTful JSON API for services, optionally a GraphQL gateway later.
- JWT access tokens (short-lived); refresh tokens stored in Redis.
- All state-changing endpoints require authentication.
- Webhooks are idempotent and verify provider signatures.

---

## Authentication & Session Management
### POST /auth/signup
- Public
- Body: { name, email, password, role } (role in ["client","chef"])
- Response: { user, access_token, refresh_token_id }

### POST /auth/login
- Body: { email, password }
- Response: { access_token (JWT), refresh_token_id }
- Server side: store refresh token metadata in Redis keyed by `refresh_token_id`

### POST /auth/refresh
- Body: { refresh_token_id }
- Behavior: validate in Redis, issue new access token and optionally new refresh token id (rotate), update Redis

### POST /auth/logout
- Body: { refresh_token_id }
- Behavior: delete refresh_token_id from Redis (revoke)

### GET /auth/me
- Requires access token
- Returns user profile and current active sessions/devices

### POST /auth/request-password-reset
### POST /auth/reset-password

Notes:
- Use PKCE & OAuth for mobile if adding mobile apps.
- Use email verification and optional MFA.

---

## Users & Chef Profiles
### GET /chefs
- Query params: `q`, `cuisine`, `diet`, `lat`, `lng`, `radius`, `sort`, `page`, `limit`
- Use search index (ElasticSearch); cached query results in Redis with a TTL

### GET /chefs/{chefId}
- Returns chef profile, menus, rating summary
- Cache per profile, TTL 5–30 min; invalidate on profile/menu/review changes

### POST /chefs
- Create/update chef profile (auth: chef)

### POST /chefs/{chefId}/media
- Direct upload flow: request signed S3 URL, client uploads

---

## Menus & Menu Items
### POST /chefs/{chefId}/menus
### GET /chefs/{chefId}/menus
### GET /menus/{id}

---

## Bookings
### POST /bookings
- Body: { chef_id, menu_id, event_date, start_time, end_time, requirements, client_shipping_venue_id }
- Behavior:
  - Create booking as `pending`
  - Reserve slot (use DB transaction + Redis lock to avoid double-booking)
  - Optionally return payment intent

### GET /bookings (user)
- Returns bookings for the user (client) or incoming bookings for chef

### GET /bookings/{id}
### PATCH /bookings/{id}
- Status transitions with auth checks (chef can confirm, client can cancel within rules)

Idempotency:
- Use Idempotency-Key header for create endpoints (payments, bookings) to avoid duplicates.

---

## Payments (Stripe, PayPal, M-Pesa)
### POST /payments/intent
- Body: { booking_id, provider: "stripe"|"paypal"|"mpesa", return_url? }
- Response:
  - Stripe: { checkout_url or client_secret }
  - M-Pesa (STK Push): triggers push to customer's phone and returns a payment request id

### Webhook endpoints
- POST /webhooks/stripe
- POST /webhooks/paypal
- POST /webhooks/mpesa
- Behavior:
  - Verify signature
  - Lookup idempotency by provider transaction id
  - Mark payment record and update booking status
  - Publish event to message queue for downstream work (notifications, payouts)

### M-Pesa specifics (adapter)
- M-Pesa flows to implement:
  - **STK Push (Lipa na M-Pesa Online)** – for immediate customer prompt
  - **C2B** – for customer-initiated payments via M-Pesa paybill
  - **B2C** – platform-to-customer/chef payouts
- Endpoints:
  - POST /payments/mpesa/stk (initiate an STK Push)
    - Body: { booking_id, phone_number, amount }
  - POST /webhooks/mpesa/confirmation  (provider calls back with payment result)
    - Ensure callback signature verification and idempotent processing
  - POST /payments/mpesa/b2c (payout to chefs) — admin or scheduled from payouts service

Security:
- Store M-Pesa API credentials in Secrets Manager and rotate periodically
- Keep a retry and reconciliation job for unconfirmed payments

---

## Reviews
### POST /bookings/{bookingId}/reviews
- Auth: client who completed the booking
- Body: { rating, comment }
- On create: publish job to recompute chef rating and invalidate chef profile cache

---

## Messaging
### POST /conversations
- Body: { booking_id }
### GET /conversations/{id}/messages
### POST /conversations/{id}/messages
- Use WebSockets for real-time delivery; fallback to polling via REST

---

## Admin & Billing
### GET /admin/transactions
### POST /admin/payouts
- Admin endpoints to approve & process payouts (B2C) to chefs

---

## Infrastructure Endpoints (internal)
- Health checks: `GET /healthz` (liveness), `GET /ready` (readiness)
- Metrics: `/metrics` for Prometheus
- Tracing headers: support `traceparent` via OpenTelemetry

---

## Error Handling & Status Codes
- Standardized error envelope:
  ```json
  { "error": { "code": "BOOKING_CONFLICT", "message": "Slot already reserved", "details": {...} } }
