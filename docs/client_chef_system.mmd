flowchart LR
    subgraph Client
        A1[Browse Chefs or Search]
        A2[Sign Up or Login]
        A3[Select Menu and Date Time]
        A4[Choose Payment Method]
        A5[Chat with Chef]
        A6[Leave Review]
    end

    subgraph Chef
        B1[Sign Up as Chef]
        B2[Complete Onboarding]
        B3[Accept or Reject Booking]
        B4[Deliver Service]
        B5[Collect Reviews]
    end

    subgraph System
        C1[Admin Verification]
        C2[Create Booking Record]
        C3[Payment Confirmation]
        C4[Send Notifications and Reminders]
        C5[Release Earnings]
        C6[Recommendation Engine]
    end

    %% Flow connections
    A1 --> A2 --> A3 --> C2 --> A4 --> C3
    C3 -->|Payment Success| A5
    C3 -->|Payment Success| B3
    B3 -->|Accept| A5
    A5 --> B4 --> C5 --> B5
    B4 --> A6 --> C6
    C1 --> B2
    C4 --> A5
