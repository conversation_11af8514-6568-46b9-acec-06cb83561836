# Mesaka – MVP Feature List (PRD-lite)

## Core MVP Features
1. **User Accounts & Roles**
   - Registration & login (client/chef roles).
   - Profile management (basic info).
2. **Chef Onboarding**
   - Create chef profile (bio, specialties, pricing).
   - Upload portfolio photos.
   - Verification workflow (manual approval for <PERSON>).
3. **Discovery**
   - Search/filter by cuisine, location, dietary needs.
   - View chef profile with photos and pricing.
4. **Booking System**
   - Select date/time, choose menu, add requirements.
   - Booking request → chef confirms → client pays.
5. **Payments**
   - Secure payment integration (Stripe/PayPal).
   - Payment status (pending, paid).
6. **Reviews & Ratings**
   - Clients leave rating (1–5 stars) + comment after booking.
7. **Messaging**
   - Basic chat tied to a booking for coordination.

## Out of Scope for MVP (Future Phases)
- AI-powered recommendations.
- Corporate bulk event management.
- Gamification & loyalty rewards.
- AR/VR menu previews.
- Automated recurring meal plans.
