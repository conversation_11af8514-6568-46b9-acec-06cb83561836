journey
    title Mesaka User Journey Map
    section Chef Onboarding
      Sign up & create account: 5:Chef
      Upload certifications & portfolio: 4:Chef
      Profile verification & approval: 3:Chef
      Gain visibility on platform: 4:Chef

    section Client Discovery & Booking
      Client browses homepage with AI recommendations: 5:Client
      Filter by cuisine, dietary needs, location: 4:Client
      View chef profile with ratings & portfolio: 5:Client
      Start chat or consultation: 4:Client
      Confirm booking & customize menu: 5:Client

    section Corporate Catering
      Corporate client submits event request: 5:Corporate Client
      Match with relevant chefs: 4:System
      Review proposals & finalize contract: 4:Corporate Client
      Payment & confirmation: 5:Corporate Client

    section Meal Service Setup
      Schedule regular meal plans: 5:Client
      Set preferences & dietary restrictions: 4:Client
      Real-time availability sync: 4:<PERSON>
      Chef prepares recurring service: 5:Chef

    section Payment & Reviews
      Secure payment processing: 5:Client, Chef
      Service completed & feedback request: 4:Client
      Client leaves review & rating: 5:Client
      Chef visibility boosted by good reviews: 5:Chef
