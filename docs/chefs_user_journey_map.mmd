journey
    title Mesaka – User Flow Journey
    section Landing
      Visit homepage with search & featured chefs: 5:<PERSON><PERSON>, Chef
      Explore trust signals & categories: 4:Client
    section Exploration
      Search by cuisine/location/dietary needs: 5:Client
      Browse chef cards & click profile: 4:Client
      View portfolio, certifications, menus: 5:Client
    section Account & Access
      Sign up / Login (email, Google, phone): 5:<PERSON><PERSON>, Chef
      Chef onboarding (bio, menus, pricing, certifications): 5:Chef
      <PERSON><PERSON> verifies chef profile: 4:Chef
    section Booking & Payment
      Select menu & customize dishes: 5:Client
      Pick date/time/venue → check availability: 4:Client
      Booking created as pending: 5:System
      Choose payment (Stripe, PayPal, M-Pesa): 5:Client
      Payment confirmed → booking confirmed: 5:System
    section Pre-Event
      Chat opens between client & chef: 5:Client, Chef
      Notifications & reminders sent: 4:System
    section Service & Review
      Chef delivers service: 5:Chef
      Client leaves rating & review: 5:Client
      Chef’s visibility boosted by reviews: 4:System
    section Retention
      Recommendations shown for next booking: 4:System
      Loyalty points / referrals offered: 4:Client

