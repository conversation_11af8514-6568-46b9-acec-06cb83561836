sequenceDiagram
    participant Client
    participant Frontend
    participant Backend
    participant PaymentService
    participant Chef

    Client->>Frontend: Browse chefs & select one
    Frontend->>Backend: Request chef details
    Backend-->>Frontend: Return chef profile & availability

    Client->>Frontend: Initiates booking
    Frontend->>Backend: Create booking (pending)
    Backend-->>Frontend: Booking ID returned

    Client->>Frontend: Chooses payment method
    Frontend->>Backend: Request payment session
    Backend->>PaymentService: Create payment intent
    PaymentService-->>Backend: Payment confirmation
    Backend-->>Frontend: Payment success
    Backend->>Chef: Notify of confirmed booking

    Client-->>Chef: Chat & coordinate
    Chef->>Client: Deliver service
    Backend->>Client: Prompt for review
    Client->>Backend: Submit review
    Backend->>Chef: Update rating & profile
