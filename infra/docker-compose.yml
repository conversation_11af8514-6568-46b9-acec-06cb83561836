version: "3.9"
services:
  postgres:
    image: postgres:15
    container_name: mesaka_postgres
    environment:
      POSTGRES_USER: mesaka
      POSTGRES_PASSWORD: mesaka_pass
      POSTGRES_DB: mesaka_db
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./infra/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7
    container_name: mesaka_redis
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data

volumes:
  pgdata:
  redisdata:
