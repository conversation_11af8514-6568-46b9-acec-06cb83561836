openapi: 3.0.3
info:
  title: Mesaka API
  description: |
    Mesaka – Chef-Client Marketplace API.
    Provides endpoints for authentication, chef discovery, bookings, payments (Stripe/PayPal/M-Pesa), reviews, and messaging.
  version: 1.0.0
servers:
  - url: https://api.mesaka.com/v1
    description: Production server
  - url: https://sandbox.api.mesaka.com/v1
    description: Sandbox server

tags:
  - name: Authentication
  - name: Users & Chefs
  - name: Menus
  - name: Bookings
  - name: Payments
  - name: Reviews
  - name: Messaging

paths:
  /auth/signup:
    post:
      tags: [Authentication]
      summary: Register new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name: { type: string }
                email: { type: string, format: email }
                password: { type: string, format: password }
                role: { type: string, enum: [client, chef] }
      responses:
        '201':
          description: User created

  /auth/login:
    post:
      tags: [Authentication]
      summary: Login with email/password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email: { type: string }
                password: { type: string }
      responses:
        '200':
          description: Tokens returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token: { type: string }
                  refresh_token_id: { type: string }

  /auth/refresh:
    post:
      tags: [Authentication]
      summary: Refresh access token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token_id: { type: string }
      responses:
        '200':
          description: New tokens returned

  /chefs:
    get:
      tags: [Users & Chefs]
      summary: Search/filter chefs
      parameters:
        - in: query
          name: cuisine
          schema: { type: string }
        - in: query
          name: diet
          schema: { type: string }
        - in: query
          name: location
          schema: { type: string }
      responses:
        '200':
          description: List of chefs
    post:
      tags: [Users & Chefs]
      summary: Create/update chef profile
      responses:
        '201':
          description: Chef profile created

  /chefs/{id}:
    get:
      tags: [Users & Chefs]
      summary: Get chef profile by ID
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: integer }
      responses:
        '200':
          description: Chef profile

  /bookings:
    post:
      tags: [Bookings]
      summary: Create a booking
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                chef_id: { type: integer }
                menu_id: { type: integer }
                event_date: { type: string, format: date }
                start_time: { type: string }
                end_time: { type: string }
                requirements: { type: string }
      responses:
        '201':
          description: Booking created
    get:
      tags: [Bookings]
      summary: List user bookings
      responses:
        '200':
          description: Array of bookings

  /bookings/{id}:
    get:
      tags: [Bookings]
      summary: Get booking details
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: integer }
      responses:
        '200':
          description: Booking details
    patch:
      tags: [Bookings]
      summary: Update booking status
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: string, enum: [pending, confirmed, cancelled, completed] }
      responses:
        '200':
          description: Booking updated

  /payments/intent:
    post:
      tags: [Payments]
      summary: Create payment intent (Stripe/PayPal/M-Pesa)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                booking_id: { type: integer }
                provider: { type: string, enum: [stripe, paypal, mpesa] }
      responses:
        '200':
          description: Payment session details

  /webhooks/mpesa:
    post:
      tags: [Payments]
      summary: Handle M-Pesa confirmation webhook
      responses:
        '200':
          description: Webhook processed

  /bookings/{id}/review:
    post:
      tags: [Reviews]
      summary: Submit review
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                rating: { type: integer, minimum: 1, maximum: 5 }
                comment: { type: string }
      responses:
        '201':
          description: Review submitted

  /conversations:
    post:
      tags: [Messaging]
      summary: Start conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                booking_id: { type: integer }
      responses:
        '201':
          description: Conversation created

  /conversations/{id}/messages:
    get:
      tags: [Messaging]
      summary: Get conversation messages
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: integer }
      responses:
        '200':
          description: List of messages
    post:
      tags: [Messaging]
      summary: Send message
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                body: { type: string }
      responses:
        '201':
          description: Message sent
